const connectionString =
  'InstrumentationKey=941d87e4-51d6-4aa3-b179-edcdc0847d1c;IngestionEndpoint=https://centralus-2.in.applicationinsights.azure.com/;LiveEndpoint=https://centralus.livediagnostics.monitor.azure.com/';
export const environment = {
  production: true,
  version: require('../../package.json').version,
  platform: 'staging',
  apiUrl: 'https://edgefactorapi.cbos.co.za/v1/',
  appUrl: 'https://edgefactor.cbos.co.za/',
  contentUrl: 'https://ef-staging-f5esbhcphbhcg0fz.z03.azurefd.net/v1/',
  signalRUrl: 'https://edgefactorr.cbos.co.za/',
  appInsights: {
    connectionString: connectionString,
  },
  client_id: 'edgefactor',
  authority_ca: 'https://edgefactoridca.cbos.co.za',
  authority_usa: 'https://edgefactoridusa.cbos.co.za',
  redirect_uri: 'https://edgefactor.cbos.co.za/auth-callback',
  post_logout_redirect_uri: 'https://edgefactor.cbos.co.za/',
  silent_redirect_uri: 'https://edgefactor.cbos.co.za/silent-callback.html',
  convey_this_api_key: 'pub_de84665e79ea7a4dd10c6da9101c77b0',
  googleMapApiKey: 'AIzaSyCyixB17FQW8ciiMIP-qaho7DVjSmodiFQ',
  html5Path: 'https://app.edgefactor.com/html5',
  clarityId: 'qxblpzdf3q',
  showShareBtn: true,
};
